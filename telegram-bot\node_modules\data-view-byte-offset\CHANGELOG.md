# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.1](https://github.com/inspect-js/data-view-byte-offset/compare/v1.0.0...v1.0.1) - 2024-12-18

### Commits

- [types] use shared tsconfig [`d5ce484`](https://github.com/inspect-js/data-view-byte-offset/commit/d5ce484f91818961c0521d98510131f6e2bb8d84)
- [readme] update URLs [`f297dd2`](https://github.com/inspect-js/data-view-byte-offset/commit/f297dd25fe9e018c6dc4fa181f723af4153e53e0)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`f89d994`](https://github.com/inspect-js/data-view-byte-offset/commit/f89d994b4f94fca0f75a5afe4e07963a379ff36e)
- [actions] split out node 10-20, and 20+ [`e648e1d`](https://github.com/inspect-js/data-view-byte-offset/commit/e648e1da732dad4702184ffb2d2aaa63e5b75ec5)
- [Refactor] use `call-bound` directly [`56d996e`](https://github.com/inspect-js/data-view-byte-offset/commit/56d996ee5ce6a896215c4d9ffadbeda2605a8988)
- [Dev Deps] update `@arethetypeswrong/cli`, `tape` [`63abffa`](https://github.com/inspect-js/data-view-byte-offset/commit/63abffac147beb7917c538122f05dd007f34b41e)
- [Tests] replace `aud` with `npm audit` [`a7c11a5`](https://github.com/inspect-js/data-view-byte-offset/commit/a7c11a54fdfb219b938a04770a8f9c3db36b848f)
- [Deps] update `call-bind` [`b86a774`](https://github.com/inspect-js/data-view-byte-offset/commit/b86a7743d663ee70c4049c134625913e723e570e)
- [Dev Deps] update `@ljharb/tsconfig` [`058bb63`](https://github.com/inspect-js/data-view-byte-offset/commit/058bb6320cc183f48c096f7e8b7d3e13db420805)
- [Deps] update `call-bind` [`6593944`](https://github.com/inspect-js/data-view-byte-offset/commit/6593944952f2064ce3a6c48237da1610fdd169f5)
- [Dev Deps] add missing peer dep [`402219e`](https://github.com/inspect-js/data-view-byte-offset/commit/402219ed9c86ec5046892bc1248423cb34672c20)

## v1.0.0 - 2024-03-04

### Commits

- Initial implementation, tests, readme, types [`8b94518`](https://github.com/inspect-js/data-view-byte-offset/commit/8b94518cd2a87df3084cdf60b52f70d9f65b94b6)
- Initial commit [`aee2acc`](https://github.com/inspect-js/data-view-byte-offset/commit/aee2accbbefcd5645693f4587ce2eabde166b1a0)
- npm init [`10a21a4`](https://github.com/inspect-js/data-view-byte-offset/commit/10a21a4189c51a3add252e3f76fe31a0b5bdcfc1)
- Only apps should have lockfiles [`f6cfa3e`](https://github.com/inspect-js/data-view-byte-offset/commit/f6cfa3e917d58c2e130f9383f5e04f5d5069d0e6)
